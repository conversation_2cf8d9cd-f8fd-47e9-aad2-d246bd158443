<template>
	<view class="main_box">
		<u-button type="success" open-type="getUserInfo" shape="circle"
			@getuserinfo="handleGetuserinfo">微信一键登录</u-button>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				phone:''
			};
		},
		methods: {
			handleGetuserinfo(e) {
				console.log(e);
				this.wxlogin()
			},
			wxlogin() {
				uni.showLoading({
					title: '登录中'
				});
				uni.login({
					complete: (res) => {

						this.$u.api
							.wxToken({
								code: res.code,
								phone:this.phone
							})
							.then(data => {

								// this.$u.func.log(data)
								this.$u.func.login(data)
								uni.hideLoading()
								this.$u.func.showToast({
									title: '登录成功',
									success: () => {
										this.$u.func.redirect(
											'/pages/index/index'
										)
									}
								})
							})
							.catch(err => {
								uni.hideLoading()
								this.$u.func.showToast({
									title: err
								})
							})
					}
				})
			}
		}
	}
</script>

<style lang="scss" scoped>
	.main_box {
		padding: 30rpx;
		box-sizing: border-box;
		min-height: 100vh;
		border-radius: 10rpx;
		display: flex;
		align-items: center;
	}
</style>