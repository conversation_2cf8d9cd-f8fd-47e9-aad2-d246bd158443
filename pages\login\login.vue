<template>
	<view class="login-container">
		<!-- 背景装饰 -->
		<view class="bg-decoration">
			<view class="circle circle-1"></view>
			<view class="circle circle-2"></view>
			<view class="circle circle-3"></view>
		</view>

		<!-- 登录卡片 -->
		<view class="login-card">
			<!-- Logo和标题 -->
			<view class="header-section">
				<view class="logo-container">
					<view class="logo-bg">
						<u-icon name="account-fill" size="80" color="#fff"></u-icon>
					</view>
				</view>
				<view class="title">欢迎登录</view>
				<view class="subtitle">请输入手机号或使用微信快捷登录</view>
			</view>

			<!-- 表单区域 -->
			<view class="form-section">
				<u-form :model="form" ref="uForm" :rules="rules">
					<u-form-item prop="phone" borderBottom>
						<u-input
							v-model="form.phone"
							placeholder="请输入手机号"
							type="number"
							maxlength="11"
							prefixIcon="phone"
							clearable
							:customStyle="inputStyle"
							@focus="onInputFocus"
							@blur="onInputBlur"
						>
							<template #suffix>
								<u-button
									v-if="form.phone && !isValidPhone"
									text="格式错误"
									type="error"
									size="mini"
									disabled
								></u-button>
								<u-button
									v-else-if="form.phone && isValidPhone"
									text="格式正确"
									type="success"
									size="mini"
									disabled
								></u-button>
							</template>
						</u-input>
					</u-form-item>
				</u-form>

				<!-- 登录按钮 -->
				<view class="button-section">
					<u-button
						:text="loginButtonText"
						type="primary"
						size="large"
						:loading="isLoading"
						:disabled="!canLogin"
						@click="handlePhoneLogin"
						:customStyle="primaryButtonStyle"
					></u-button>
				</view>

				<!-- 分割线 -->
				<view class="divider-section">
					<view class="divider-line"></view>
					<view class="divider-text">或</view>
					<view class="divider-line"></view>
				</view>

				<!-- 微信登录区域 -->
				<view class="wechat-section">
					<!-- <u-button
						text="微信一键登录"
						type="success"
						size="large"
						open-type="getUserInfo"
						@getuserinfo="handleGetuserinfo"
						:customStyle="wechatButtonStyle"
						icon="weixin-fill"
					></u-button> -->

					<!-- 快捷获取手机号 -->
					<view class="quick-phone-section" v-if="!form.phone">
						<!-- <view class="quick-phone-text">还没有手机号？</view> -->
						<u-button
							text="微信授权获取"
							type="info"
							size="small"
							open-type="getPhoneNumber"
							@getphonenumber="getPhoneNumber"
							plain
						></u-button>
					</view>
				</view>
			</view>
		</view>

		<!-- 底部提示 -->
		<view class="footer-section">
			<view class="privacy-text">
				登录即表示同意
				<text class="link-text">《用户协议》</text>
				和
				<text class="link-text">《隐私政策》</text>
			</view>
		</view>
	</view>
</template>

<script>
	export default {
		data() {
			return {
				form: {
					phone: ''
				},
				isLoading: false,
				isInputFocused: false,
				rules: {
					phone: [
						{
							required: true,
							message: '请输入手机号',
							trigger: ['blur', 'change']
						},
						{
							pattern: /^1[3-9]\d{9}$/,
							message: '手机号格式不正确',
							trigger: ['blur', 'change']
						}
					]
				},
				inputStyle: {
					fontSize: '16px',
					padding: '12px 0'
				},
				primaryButtonStyle: {
					background: 'linear-gradient(135deg, #667eea 0%, #c3cfe2 100%)',
					borderRadius: '25px',
					height: '50px',
					fontSize: '16px',
					fontWeight: 'bold'
				},
				wechatButtonStyle: {
					borderRadius: '25px',
					height: '50px',
					fontSize: '16px',
					fontWeight: 'bold'
				}
			};
		},
		computed: {
			isValidPhone() {
				return /^1[3-9]\d{9}$/.test(this.form.phone);
			},
			canLogin() {
				return this.form.phone && this.isValidPhone && !this.isLoading;
			},
			loginButtonText() {
				if (this.isLoading) return '登录中...';
				if (!this.form.phone) return '请输入手机号';
				if (!this.isValidPhone) return '手机号格式错误';
				return '手机号登录';
			}
		},
		methods: {
			// 手机号登录
			handlePhoneLogin() {
				if (!this.canLogin) return;

				this.$refs.uForm.validate().then(valid => {
					if (valid) {
						this.isLoading = true;
						this.wxlogin();
					}
				}).catch(errors => {
					console.log('表单验证失败：', errors);
				});
			},

			// 微信授权登录
			handleGetuserinfo(e) {
				console.log('微信用户信息：', e);
				this.isLoading = true;
				this.wxlogin();
			},

			// 获取微信手机号
			getPhoneNumber(e) {
				console.log('获取手机号：', e);
				const { code } = e.detail;
				if (code) {
					uni.showLoading({
						title: '获取中...'
					});
					this.$u.api.userPhone(code).then(res => {
						uni.hideLoading();
						this.form.phone = res.data.phoneNumber;
						this.$u.func.showToast({
							title: '手机号获取成功'
						});
					}).catch(() => {
						uni.hideLoading();
						this.$u.func.showToast({
							title: '获取手机号失败'
						});
					});
				} else {
					this.$u.func.showToast({
						title: '用户取消授权'
					});
				}
			},

			// 输入框聚焦事件
			onInputFocus() {
				this.isInputFocused = true;
			},

			// 输入框失焦事件
			onInputBlur() {
				this.isInputFocused = false;
			},

			// 微信登录逻辑
			wxlogin() {
				uni.login({
					complete: (res) => {
						this.$u.api
							.wxToken({
								code: res.code,
								phone: this.form.phone
							})
							.then(data => {
								this.$u.func.login(data);
								this.isLoading = false;
								this.$u.func.showToast({
									title: '登录成功',
									success: () => {
										setTimeout(() => {
											this.$u.func.redirect('/pages/index/index');
										}, 1000);
									}
								});
							})
							.catch(err => {
								this.isLoading = false;
								this.$u.func.showToast({
									title: err || '登录失败'
								});
							});
					}
				});
			}
		}
	}
</script>

<style lang="scss" scoped>
	.login-container {
		min-height: 100vh;
		background: linear-gradient(135deg, #667eea 0%, #c3cfe2 100%);
		position: relative;
		display: flex;
		flex-direction: column;
		justify-content: center;
		align-items: center;
		padding: 40rpx;
		box-sizing: border-box;
	}

	// 背景装饰
	.bg-decoration {
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		bottom: 0;
		overflow: hidden;
		z-index: 1;

		.circle {
			position: absolute;
			border-radius: 50%;
			background: rgba(255, 255, 255, 0.1);
			animation: float 6s ease-in-out infinite;

			&.circle-1 {
				width: 200rpx;
				height: 200rpx;
				top: 10%;
				left: -50rpx;
				animation-delay: 0s;
			}

			&.circle-2 {
				width: 150rpx;
				height: 150rpx;
				top: 60%;
				right: -30rpx;
				animation-delay: 2s;
			}

			&.circle-3 {
				width: 100rpx;
				height: 100rpx;
				top: 30%;
				right: 20%;
				animation-delay: 4s;
			}
		}
	}

	@keyframes float {
		0%, 100% {
			transform: translateY(0px) rotate(0deg);
		}
		50% {
			transform: translateY(-20px) rotate(180deg);
		}
	}

	// 登录卡片
	.login-card {
		background: rgba(255, 255, 255, 0.95);
		backdrop-filter: blur(10px);
		border-radius: 30rpx;
		padding: 60rpx 40rpx;
		width: 100%;
		max-width: 600rpx;
		box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
		position: relative;
		z-index: 2;

		// 卡片内部渐变边框效果
		&::before {
			content: '';
			position: absolute;
			top: 0;
			left: 0;
			right: 0;
			bottom: 0;
			border-radius: 30rpx;
			padding: 2rpx;
			background: linear-gradient(135deg, #667eea, #c3cfe2);
			mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
			mask-composite: exclude;
			z-index: -1;
		}
	}

	// 头部区域
	.header-section {
		text-align: center;
		margin-bottom: 60rpx;

		.logo-container {
			margin-bottom: 30rpx;
			display: flex;
			justify-content: center;

			.logo-bg {
				width: 120rpx;
				height: 120rpx;
				background: linear-gradient(135deg, #667eea 0%, #c3cfe2 100%);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.3);
				animation: pulse 2s ease-in-out infinite;
			}
		}

		.title {
			font-size: 36rpx;
			font-weight: bold;
			color: #333;
			margin-bottom: 15rpx;
		}

		.subtitle {
			font-size: 28rpx;
			color: #666;
			line-height: 1.5;
		}
	}

	@keyframes pulse {
		0%, 100% {
			transform: scale(1);
		}
		50% {
			transform: scale(1.05);
		}
	}

	// 表单区域
	.form-section {
		.button-section {
			margin: 40rpx 0;
		}

		// 分割线
		.divider-section {
			display: flex;
			align-items: center;
			margin: 40rpx 0;

			.divider-line {
				flex: 1;
				height: 1rpx;
				background: #e0e0e0;
			}

			.divider-text {
				margin: 0 30rpx;
				font-size: 24rpx;
				color: #999;
			}
		}

		// 微信登录区域
		.wechat-section {
			.quick-phone-section {
				margin-top: 30rpx;
				text-align: center;

				.quick-phone-text {
					font-size: 24rpx;
					color: #666;
					margin-bottom: 15rpx;
				}
			}
		}
	}

	// 底部区域
	.footer-section {
		position: absolute;
		bottom: 60rpx;
		left: 50%;
		transform: translateX(-50%);
		z-index: 2;

		.privacy-text {
			font-size: 22rpx;
			color: rgba(255, 255, 255, 0.8);
			text-align: center;
			line-height: 1.5;

			.link-text {
				color: #fff;
				text-decoration: underline;
			}
		}
	}

	// 响应式适配
	@media screen and (max-height: 800px) {
		.login-container {
			padding: 20rpx;
		}

		.login-card {
			padding: 40rpx 30rpx;
		}

		.header-section {
			margin-bottom: 40rpx;

			.logo-container {
				margin-bottom: 20rpx;
			}
		}

		.footer-section {
			bottom: 30rpx;
		}
	}

	// 深色模式适配
	@media (prefers-color-scheme: dark) {
		.login-card {
			background: rgba(30, 30, 30, 0.95);

			.title {
				color: #fff;
			}

			.subtitle {
				color: #ccc;
			}
		}

		.divider-line {
			background: #444 !important;
		}

		.divider-text {
			color: #ccc !important;
		}

		.quick-phone-text {
			color: #ccc !important;
		}
	}
</style>