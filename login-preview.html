<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>登录页面预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            overflow: hidden;
        }
        
        /* 背景装饰 */
        .bg-decoration {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            overflow: hidden;
            z-index: 1;
        }
        
        .circle {
            position: absolute;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.1);
            animation: float 6s ease-in-out infinite;
        }
        
        .circle-1 {
            width: 100px;
            height: 100px;
            top: 10%;
            left: -25px;
            animation-delay: 0s;
        }
        
        .circle-2 {
            width: 75px;
            height: 75px;
            top: 60%;
            right: -15px;
            animation-delay: 2s;
        }
        
        .circle-3 {
            width: 50px;
            height: 50px;
            top: 30%;
            right: 20%;
            animation-delay: 4s;
        }
        
        @keyframes float {
            0%, 100% {
                transform: translateY(0px) rotate(0deg);
            }
            50% {
                transform: translateY(-10px) rotate(180deg);
            }
        }
        
        /* 登录卡片 */
        .login-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 15px;
            padding: 40px 30px;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
            position: relative;
            z-index: 2;
            margin: 20px;
        }
        
        /* 头部区域 */
        .header-section {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .logo-container {
            margin-bottom: 20px;
            display: flex;
            justify-content: center;
        }
        
        .logo-bg {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
            animation: pulse 2s ease-in-out infinite;
            color: white;
            font-size: 32px;
        }
        
        @keyframes pulse {
            0%, 100% {
                transform: scale(1);
            }
            50% {
                transform: scale(1.05);
            }
        }
        
        .title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }
        
        .subtitle {
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }
        
        /* 表单区域 */
        .form-section {
            margin-bottom: 30px;
        }
        
        .input-group {
            margin-bottom: 20px;
            position: relative;
        }
        
        .input-field {
            width: 100%;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 25px;
            font-size: 16px;
            outline: none;
            transition: all 0.3s ease;
        }
        
        .input-field:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 15px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        
        .btn-success {
            background: #07c160;
            color: white;
        }
        
        .btn-success:hover {
            background: #06ad56;
            transform: translateY(-2px);
        }
        
        /* 分割线 */
        .divider {
            display: flex;
            align-items: center;
            margin: 30px 0;
        }
        
        .divider-line {
            flex: 1;
            height: 1px;
            background: #e0e0e0;
        }
        
        .divider-text {
            margin: 0 15px;
            font-size: 12px;
            color: #999;
        }
        
        /* 快捷获取手机号 */
        .quick-phone {
            text-align: center;
            margin-top: 15px;
        }
        
        .quick-phone-text {
            font-size: 12px;
            color: #666;
            margin-bottom: 8px;
        }
        
        .btn-small {
            padding: 8px 16px;
            font-size: 12px;
            background: transparent;
            color: #667eea;
            border: 1px solid #667eea;
        }
        
        /* 底部提示 */
        .footer {
            position: absolute;
            bottom: 30px;
            left: 50%;
            transform: translateX(-50%);
            z-index: 2;
            text-align: center;
        }
        
        .privacy-text {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.8);
            line-height: 1.5;
        }
        
        .link-text {
            color: #fff;
            text-decoration: underline;
        }
        
        /* 响应式 */
        @media (max-height: 600px) {
            .login-card {
                padding: 30px 25px;
            }
            
            .header-section {
                margin-bottom: 30px;
            }
            
            .footer {
                bottom: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="bg-decoration">
        <div class="circle circle-1"></div>
        <div class="circle circle-2"></div>
        <div class="circle circle-3"></div>
    </div>
    
    <div class="login-card">
        <div class="header-section">
            <div class="logo-container">
                <div class="logo-bg">👤</div>
            </div>
            <div class="title">欢迎登录</div>
            <div class="subtitle">请输入手机号或使用微信快捷登录</div>
        </div>
        
        <div class="form-section">
            <div class="input-group">
                <input type="tel" class="input-field" placeholder="请输入手机号" maxlength="11">
            </div>
            
            <button class="btn btn-primary">手机号登录</button>
            
            <div class="divider">
                <div class="divider-line"></div>
                <div class="divider-text">或</div>
                <div class="divider-line"></div>
            </div>
            
            <button class="btn btn-success">🔗 微信一键登录</button>
            
            <div class="quick-phone">
                <div class="quick-phone-text">还没有手机号？</div>
                <button class="btn btn-small">微信授权获取</button>
            </div>
        </div>
    </div>
    
    <div class="footer">
        <div class="privacy-text">
            登录即表示同意
            <span class="link-text">《用户协议》</span>
            和
            <span class="link-text">《隐私政策》</span>
        </div>
    </div>
</body>
</html>
