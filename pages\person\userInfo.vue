<template>
	<view style="padding: 20rpx;box-sizing: border-box;">
		<u-cell-group>
			<u-cell title="头像">
				<template #value>
					<button
						style="display: flex;align-items: center;justify-content: center;width: 70rpx; height: 70rpx;padding: 5rpx;"
						open-type="chooseAvatar" @chooseavatar="onChooseAvatar">
						<image :src="userInfo.avatar" style="width: 70rpx; height: 70rpx;">
						</image>
					</button>

				</template>
			</u-cell>
			<u-cell title="昵称" value="">
				<template #value>
					<u-input v-model="userInfo.name" placeholder="请输入昵称" type="nickname" border></u-input>
				</template>
			</u-cell>
			<u-cell title="真实姓名" value="">
				<template #value>
					<u-input v-model="userInfo.realName" placeholder="请输入姓名" type="nickname" border></u-input>
				</template>
			</u-cell>
			<u-cell title="手机号" value="">
				<template #value>
					<u-input style="width: 300rpx;" v-model="userInfo.phone" placeholder="请输入手机号" type='number' border>
						<template slot="suffix">
							<u-button :text="'快捷获取'" @getphonenumber="getphonenumber" type="primary"
								open-type="getPhoneNumber" size="mini"></u-button>
						</template>
					</u-input>
				</template>
			</u-cell>
		</u-cell-group>
		<view class="" style="margin-top: 20rpx;">
			<u-button size="large" type="primary" @click="handleSave" text="保存"></u-button>
		</view>

	</view>
</template>

<script>
	import http from '../../http/api.js'
	export default {
		data() {
			return {
				userInfo: {
					avatar: '',
					name: '',
					phone: ''
				}
			};
		},
		onLoad() {
			this.getDetail()
		},
		methods: {
			getDetail() {
				this.$u.api.userInfo().then((res) => {
					this.userInfo = res.data
				})
			},
			onChooseAvatar(v) {
				this.userInfo.avatar = v.detail.avatarUrl
			},
			getphonenumber(v) {
				console.log(v);
				const {
					code
				} = v.detail
				if (code) {
					this.$u.api.userPhone(code).then(res => {
						this.userInfo.phone = res.data.phoneNumber
					})
				}

			},

			handleSave() {
				console.log(1111)
				let data = {
					...this.userInfo
				}
				if (this.userInfo.avatar && this.userInfo.avatar.indexOf('http') < 0) {
					// 上传头像
					const params = {
						filePath: this.userInfo.avatar,
						name: 'file',
					}
					http.upload('/blade-resource/attach/upload?fileName=', params).then(res => {
						console.log(res);
						data = {
							...this.userInfo,
							avatar: res.data.link
						}
						this.$u.api.updateUser(data).then(res => {
							uni.showToast({
								title: '操作成功',
								success() {
									setTimeout(() => {
										uni.navigateBack()
									}, 1000)
								}
							})

						})
					})
				} else {
					this.$u.api.updateUser(data).then(res => {
						uni.showToast({
							title: '操作成功',
							success() {
								setTimeout(() => {
									uni.navigateBack()
								}, 1000)
							}
						})

					})
				}
			}
		}
	}
</script>

<style lang="scss" scoped>
	.slot-image {
		height: 60rpx;
		width: 60rpx;
	}

	::v-deep input {
		text-align: right !important;
	}
</style>