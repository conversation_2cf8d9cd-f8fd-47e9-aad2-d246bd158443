{"version": 3, "sources": ["uni-app:///main.js", null, "webpack:///D:/project/vt-unih5-order/pages/index/index.vue?a7bb", "webpack:///D:/project/vt-unih5-order/pages/index/index.vue?33a6", "webpack:///D:/project/vt-unih5-order/pages/index/index.vue?1209", "uni-app:///pages/index/index.vue", "webpack:///D:/project/vt-unih5-order/pages/index/index.vue?928e", "webpack:///D:/project/vt-unih5-order/pages/index/index.vue?dd66"], "names": ["wx", "__webpack_require_UNI_MP_PLUGIN__", "__webpack_require__", "createPage", "Page", "data", "summary", "total", "pending", "processing", "finished", "tabs", "label", "value", "currentTab", "orders", "statusMap", "showSignDrawer", "locationLoading", "signLocation", "signPhoto", "signRemark", "signOrder", "qqmapsdk", "computed", "filteredOrders", "onLoad", "key", "methods", "wxlogin", "uni", "complete", "wxToken", "code", "then", "resolve", "catch", "title", "fetchOrders", "list", "id", "name", "serviceType", "serviceTime", "description", "customerName", "contactName", "contactPhone", "address", "status", "mapStatus", "handleAccept", "icon", "handleSign", "chooseLocation", "type", "success", "console", "location", "latitude", "longitude", "fail", "afterRead", "file", "item", "message", "index", "uploadFile", "filePath", "http", "res", "handleDelete", "handleClickPreview", "submitSign", "handleFinish"], "mappings": ";;;;;;;;;;;;;AAAA;AAGA;AACA;AAHA;AACAA,EAAE,CAACC,iCAAiC,GAAGC,mBAAmB;AAG1DC,UAAU,CAACC,cAAI,CAAC,C;;;;;;;;;;;;;ACLhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAkH;AAClH;AACyD;AACL;AACc;;;AAGlE;AACmK;AACnK,gBAAgB,6KAAU;AAC1B,EAAE,2EAAM;AACR,EAAE,gFAAM;AACR,EAAE,yFAAe;AACjB;AACA;AACA;AACA;AACA;AACA,EAAE,oFAAU;AACZ;AACA;;AAEA;AACe,gF;;;;;;;;;;;;ACvBf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;;;;;;;;;;;ACAA;AAAA;AAAA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AACA,aAAa,6SAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,aAAa,sTAEN;AACP,KAAK;AACL;AACA,aAAa,uSAEN;AACP,KAAK;AACL;AACA,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,GAAG;AACH;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM;AACN;AACA;AACA;AACA,OAAO;AACP;AACA;AACA;AACA;AACA;AACA;;;;;;;;;;;;;;ACrEA;AAAA;AAAA;AAAA;AAAkmB,CAAgB,+mBAAG,EAAC,C;;;;;;;;;;;;;;;;;;;;;;ACyJtnB;AACA;AACA;AAAA;AAAA;AAAA,eACA;EACAC;IACA;MACAC;QACAC;QACAC;QACAC;QACAC;MACA;MACAC,OACA;QAAAC;QAAAC;MAAA,GACA;QAAAD;QAAAC;MAAA,EACA;MACAC;MACAC;MACAC;QACAR;QACAC;QACAC;MACA;MACAO;MACAC;MACAC;MACAC;MACAC;MACAC;MACA;MACAC;IACA;EACA;EAEAC;IACAC;MACA;MACA;QAAA;MAAA;IACA;EACA;EACAC;IAAA;IAAA;MAAA;QAAA;UAAA;YAAA;cAAA;cAAA,OACA;YAAA;cACA;cACA;cACA;gBACAC;cACA;YAAA;YAAA;cAAA;UAAA;QAAA;MAAA;IAAA;EACA;EACAC;IACAC;MAAA;MACA;QACAC;UACAC;YACA,cACAC;cAAAC;YAAA,GACAC;cACA;cACAC;YACA,GACAC;cACA;gBAAAC;cAAA;YACA;UACA;QACA;MACA;IACA;IACAC;MAAA;MAAA;QAAA;QAAA;UAAA;YAAA;cAAA;gBACA;gBACAC,QACA;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA,GACA;kBACAT;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA,GACA;kBACAT;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;kBACAC;gBACA,EACA;gBACA;gBACA;gBACA;kBAAA;gBAAA;gBACA,wCACA;kBAAA;gBAAA,EACA;gBACA,sCACA;kBAAA;gBAAA,EACA;cAAA;cAAA;gBAAA;YAAA;UAAA;QAAA;MAAA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;IACA;IACA;IACAC;MACArB;QAAAO;QAAAe;MAAA;IACA;IACAC;MACA;MACA;MACA;MACA;MACA;MACA;IACA;IACAC;MAAA;MACA;MACA;MACA;MACA;MACA;MACA;;MAEA;MACA;MACA;MACA;MACAtD;QACAuD;QAEAC;UACAC;;UAEA;UACA;YACAC;cACAC;cACAC;YACA;YACAJ;cACA;cACAC;cACA;cACA;cACA;cACAA;cACAA;cACA;cACA;cACA;YACA;;YACAI;cACA;cACAJ;cACA3B;gBAAAO;gBAAAe;cAAA;YACA;UACA;QACA;QACAS;UACAJ;UACA;UACA3B;YAAAO;YAAAe;UAAA;QACA;MACA;IACA;IACAU;MAAA;MACAL;MACA;MACA;MACAM;QACA,sDACAC;UACAf;UACAgB;UACA;UACAC;QAAA,GACA;MACA;MACAH;QACA;MACA;IACA;IACAI;MAAA;MACA;QACA;UACAC;UACA3B;QACA;QACA4B;UACA;YAAA;UAAA,YACA;UACA;YAAA;UAAA,aACA;UACA;YAAA;UAAA,SACAC;QACA;MACA;IACA;IACAC;MAAA;QAAAL;QAAAzB;MACAgB;MACA;IACA;IACAe;MACAf;IACA;IACAgB;MACA;QACA3C;UAAAO;UAAAe;QAAA;QACA;MACA;MACA;QACAtB;UAAAO;UAAAe;QAAA;QACA;MACA;MACA;MACAtB;QAAAO;QAAAe;MAAA;MACA;IACA;IACAsB;MACA5C;QAAAO;QAAAe;MAAA;IACA;EACA;AACA;AAAA,2B;;;;;;;;;;;;;AC3YA;AAAA;AAAA;AAAA;AAA6oC,CAAgB,8lCAAG,EAAC,C;;;;;;;;;;;ACAjqC;AACA,OAAO,KAAU,EAAE,kBAKd", "file": "pages/index/index.js", "sourcesContent": ["import 'uni-pages';\n// @ts-ignore\nwx.__webpack_require_UNI_MP_PLUGIN__ = __webpack_require__;\nimport Vue from 'vue'\nimport Page from './pages/index/index.vue'\ncreatePage(Page)", "import { render, staticRenderFns, recyclableRender, components } from \"./index.vue?vue&type=template&id=57280228&\"\nvar renderjs\nimport script from \"./index.vue?vue&type=script&lang=js&\"\nexport * from \"./index.vue?vue&type=script&lang=js&\"\nimport style0 from \"./index.vue?vue&type=style&index=0&lang=scss&\"\n\n\n/* normalize component */\nimport normalizer from \"!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/runtime/componentNormalizer.js\"\nvar component = normalizer(\n  script,\n  render,\n  staticRenderFns,\n  false,\n  null,\n  null,\n  null,\n  false,\n  components,\n  renderjs\n)\n\ncomponent.options.__file = \"pages/index/index.vue\"\nexport default component.exports", "export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/templateLoader.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--17-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/template.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-uni-app-loader/page-meta.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=template&id=57280228&\"", "var components\ntry {\n  components = {\n    uButton: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-button/u-button\" */ \"@/uni_modules/uview-ui/components/u-button/u-button.vue\"\n      )\n    },\n    uPopup: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-popup/u-popup\" */ \"@/uni_modules/uview-ui/components/u-popup/u-popup.vue\"\n      )\n    },\n    uvUpload: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uv-upload/components/uv-upload/uv-upload\" */ \"@/uni_modules/uv-upload/components/uv-upload/uv-upload.vue\"\n      )\n    },\n    uInput: function () {\n      return import(\n        /* webpackChunkName: \"uni_modules/uview-ui/components/u-input/u-input\" */ \"@/uni_modules/uview-ui/components/u-input/u-input.vue\"\n      )\n    },\n  }\n} catch (e) {\n  if (\n    e.message.indexOf(\"Cannot find module\") !== -1 &&\n    e.message.indexOf(\".vue\") !== -1\n  ) {\n    console.error(e.message)\n    console.error(\"1. 排查组件名称拼写是否正确\")\n    console.error(\n      \"2. 排查组件是否符合 easycom 规范，文档：https://uniapp.dcloud.net.cn/collocation/pages?id=easycom\"\n    )\n    console.error(\n      \"3. 若组件不符合 easycom 规范，需手动引入，并在 components 中注册该组件\"\n    )\n  } else {\n    throw e\n  }\n}\nvar render = function () {\n  var _vm = this\n  var _h = _vm.$createElement\n  var _c = _vm._self._c || _h\n  var g0 = _vm.filteredOrders.length\n  if (!_vm._isMounted) {\n    _vm.e0 = function ($event, idx) {\n      var _temp = arguments[arguments.length - 1].currentTarget.dataset,\n        _temp2 = _temp.eventParams || _temp[\"event-params\"],\n        idx = _temp2.idx\n      var _temp, _temp2\n      _vm.currentTab = idx\n    }\n    _vm.e1 = function ($event) {\n      _vm.showSignDrawer = false\n    }\n  }\n  _vm.$mp.data = Object.assign(\n    {},\n    {\n      $root: {\n        g0: g0,\n      },\n    }\n  )\n}\nvar recyclableRender = false\nvar staticRenderFns = []\nrender._withStripped = true\n\nexport { render, staticRenderFns, recyclableRender, components }", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/babel-loader/lib/index.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--13-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/script.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=script&lang=js&\"", "<template>\r\n  <view class=\"workbench-wrap\">\r\n    <!-- 工单概况统计卡片 -->\r\n    <view class=\"summary-card\">\r\n      <view class=\"summary-title\">工单概况</view>\r\n      <view class=\"summary-stats\">\r\n        <view class=\"stat-item total\">\r\n          <view class=\"stat-label\">工单总数</view>\r\n          <view class=\"stat-value\">{{ summary.total }}</view>\r\n        </view>\r\n        <view class=\"stat-item pending\">\r\n          <view class=\"stat-label\">待接单</view>\r\n          <view class=\"stat-value\">{{ summary.pending }}</view>\r\n        </view>\r\n        <view class=\"stat-item processing\">\r\n          <view class=\"stat-label\">进行中</view>\r\n          <view class=\"stat-value\">{{ summary.processing }}</view>\r\n        </view>\r\n        <view class=\"stat-item finished\">\r\n          <view class=\"stat-label\">已完成</view>\r\n          <view class=\"stat-value\">{{ summary.finished }}</view>\r\n        </view>\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 工单tab栏 -->\r\n    <view class=\"order-tab-bar\">\r\n      <view\r\n        v-for=\"(tab, idx) in tabs\"\r\n        :key=\"tab.value\"\r\n        :class=\"['tab-item', { active: currentTab === idx }]\"\r\n        @tap=\"currentTab = idx\"\r\n      >\r\n        {{ tab.label }}\r\n      </view>\r\n    </view>\r\n\r\n    <!-- 工单列表 -->\r\n    <view class=\"order-list\">\r\n      <view v-if=\"filteredOrders.length === 0\" class=\"empty-tip\">\r\n        暂无工单\r\n      </view>\r\n      <view v-for=\"order in filteredOrders\" :key=\"order.id\" class=\"order-card\">\r\n        <view class=\"order-header\">\r\n          <view class=\"order-title\">{{ order.name }}</view>\r\n          <view class=\"order-status\" :class=\"'status-' + order.status\">{{\r\n            statusMap[order.status]\r\n          }}</view>\r\n        </view>\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">服务类型：</view>\r\n          <view class=\"order-value\">{{ order.serviceType }}</view>\r\n        </view>\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">服务时间：</view>\r\n          <view class=\"order-value\">{{ order.serviceTime }}</view>\r\n        </view>\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">工单描述：</view>\r\n          <view class=\"order-value\">{{ order.description }}</view>\r\n        </view>\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">客户名称：</view>\r\n          <view class=\"order-value\">{{ order.customerName }}</view>\r\n        </view>\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">联系人：</view>\r\n          <view class=\"order-value\">{{ order.contactName }}</view>\r\n        </view>\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">联系电话：</view>\r\n          <view class=\"order-value\">{{ order.contactPhone }}</view>\r\n        </view>\r\n        <view class=\"order-detail-row\">\r\n          <view class=\"order-label\">地址：</view>\r\n          <view class=\"order-value\">{{ order.address }}</view>\r\n        </view>\r\n        <!-- 操作区 -->\r\n        <view class=\"order-actions\">\r\n          <template v-if=\"order.status === 'pending'\">\r\n            <u-button class=\"action-btn primary\" @tap=\"handleAccept(order)\"\r\n              >接单</u-button\r\n            >\r\n          </template>\r\n          <template v-else-if=\"order.status === 'processing'\">\r\n            <u-button\r\n              class=\"action-btn\"\r\n              type=\"primary\"\r\n              plain\r\n              hairline\r\n              @tap=\"handleSign(order)\"\r\n              >签到</u-button\r\n            >\r\n            <!-- <u-button class=\"action-btn primary\" @tap=\"handleFinish(order)\">完成</u-button> -->\r\n          </template>\r\n        </view>\r\n      </view>\r\n    </view>\r\n    <!-- 签到抽屉 -->\r\n    <u-popup\r\n      :show=\"showSignDrawer\"\r\n      type=\"bottom\"\r\n      closeOnClickOverlay\r\n      @close=\"showSignDrawer = false\"\r\n      :mask-click=\"true\"\r\n      background=\"#fff\"\r\n      style=\"z-index: 9999\"\r\n    >\r\n      <view style=\"padding: 32rpx 24rpx\">\r\n        <view style=\"font-size: 32rpx; font-weight: bold; margin-bottom: 24rpx\"\r\n          >签到</view\r\n        >\r\n        <view style=\"margin-bottom: 24rpx\">\r\n          <view style=\"font-size: 28rpx; margin-bottom: 8rpx\">选择位置</view>\r\n          <u-button\r\n            @tap=\"chooseLocation\"\r\n            type=\"primary\"\r\n            :loading=\"locationLoading\"\r\n            loadingText=\"正在获取位置...\"\r\n            plain\r\n          >\r\n            {{ signLocation ? signLocation : \"点击获取位置\" }}\r\n          </u-button>\r\n        </view>\r\n        <view style=\"margin-bottom: 24rpx\">\r\n          <view style=\"font-size: 28rpx; margin-bottom: 8rpx\">上传照片</view>\r\n          <uv-upload\r\n            accept=\"media\"\r\n            @clickPreview=\"handleClickPreview\"\r\n            :fileList=\"signPhoto\"\r\n            @afterRead=\"afterRead\"\r\n            @delete=\"handleDelete\"\r\n            multiple\r\n            :maxCount=\"9\"\r\n          >\r\n          </uv-upload>\r\n        </view>\r\n        <view style=\"margin-bottom: 24rpx\">\r\n          <view style=\"font-size: 28rpx; margin-bottom: 8rpx\">备注</view>\r\n          <u-input\r\n            v-model=\"signRemark\"\r\n            placeholder=\"请输入备注\"\r\n            type=\"textarea\"\r\n            border\r\n          />\r\n        </view>\r\n        <u-button type=\"primary\" @tap=\"submitSign\">确认签到</u-button>\r\n      </view>\r\n    </u-popup>\r\n  </view>\r\n</template>\r\n\r\n<script>\r\nimport wokerOrderApi from \"@/api/wokerOrder.js\";\r\nimport QQMapWX from \"@/utils/qqmap-wx-jssdk.min.js\";\r\nimport http from '../../http/api.js'\r\nexport default {\r\n  data() {\r\n    return {\r\n      summary: {\r\n        total: 0,\r\n        pending: 0,\r\n        processing: 0,\r\n        finished: 0,\r\n      },\r\n      tabs: [\r\n        { label: \"进行中\", value: \"processing\" },\r\n        { label: \"待接单\", value: \"pending\" },\r\n      ],\r\n      currentTab: 0,\r\n      orders: [],\r\n      statusMap: {\r\n        pending: \"待接单\",\r\n        processing: \"进行中\",\r\n        finished: \"已完成\",\r\n      },\r\n      showSignDrawer: false,\r\n      locationLoading: false,\r\n      signLocation: null,\r\n      signPhoto: \"\",\r\n      signRemark: \"\",\r\n      signOrder: null,\r\n      // 初始化地图实例\r\n      qqmapsdk: null,\r\n    };\r\n  },\r\n\r\n  computed: {\r\n    filteredOrders() {\r\n      const status = this.tabs[this.currentTab].value;\r\n      return this.orders.filter((o) => o.status === status);\r\n    },\r\n  },\r\n  async onLoad() {\r\n    await this.wxlogin();\r\n    this.fetchOrders();\r\n    // 实例化，填入你申请到的 Key\r\n    this.qqmapsdk = new QQMapWX({\r\n      key: \"V37BZ-5JF63-HBZ3S-34XAJ-KPG2Q-74FPM\", // 请替换为你的真实 Key\r\n    });\r\n  },\r\n  methods: {\r\n    wxlogin() {\r\n      return new Promise((resolve) => {\r\n        uni.login({\r\n          complete: (res) => {\r\n            this.$u.api\r\n              .wxToken({ code: res.code })\r\n              .then((data) => {\r\n                this.$u.func.login(data);\r\n                resolve();\r\n              })\r\n              .catch((err) => {\r\n                this.$u.func.showToast({ title: err });\r\n              });\r\n          },\r\n        });\r\n      });\r\n    },\r\n    async fetchOrders() {\r\n      // 使用模拟数据\r\n      const list = [\r\n        {\r\n          id: 1,\r\n          name: \"门锁维修\",\r\n          serviceType: \"上门维修\",\r\n          serviceTime: \"2025-09-25 14:00\",\r\n          description: \"门锁无法正常开关，需要更换锁芯。\",\r\n          customerName: \"张三\",\r\n          contactName: \"李师傅\",\r\n          contactPhone: \"13800138000\",\r\n          address: \"北京市朝阳区望京SOHO T1-1001\",\r\n          status: \"pending\",\r\n        },\r\n        {\r\n          id: 2,\r\n          name: \"空调清洗\",\r\n          serviceType: \"家电清洗\",\r\n          serviceTime: \"2025-09-26 09:30\",\r\n          description: \"空调长时间未清洗，制冷效果差。\",\r\n          customerName: \"李四\",\r\n          contactName: \"王师傅\",\r\n          contactPhone: \"13900139000\",\r\n          address: \"上海市浦东新区陆家嘴金融中心A座\",\r\n          status: \"processing\",\r\n        },\r\n        {\r\n          id: 3,\r\n          name: \"水管漏水\",\r\n          serviceType: \"管道维修\",\r\n          serviceTime: \"2025-09-24 16:00\",\r\n          description: \"厨房水管漏水严重，需紧急处理。\",\r\n          customerName: \"王五\",\r\n          contactName: \"赵师傅\",\r\n          contactPhone: \"13700137000\",\r\n          address: \"广州市天河区珠江新城华夏路8号\",\r\n          status: \"finished\",\r\n        },\r\n      ];\r\n      this.orders = list;\r\n      this.summary.total = list.length;\r\n      this.summary.pending = list.filter((o) => o.status === \"pending\").length;\r\n      this.summary.processing = list.filter(\r\n        (o) => o.status === \"processing\"\r\n      ).length;\r\n      this.summary.finished = list.filter(\r\n        (o) => o.status === \"finished\"\r\n      ).length;\r\n    },\r\n    mapStatus(status) {\r\n      // 假设后端status: 0=待接单, 1=进行中, 2=已完成\r\n      if (status === 0) return \"pending\";\r\n      if (status === 1) return \"processing\";\r\n      if (status === 2) return \"finished\";\r\n      return \"pending\";\r\n    },\r\n    // ...existing code...\r\n    handleAccept(order) {\r\n      uni.showToast({ title: `接单：${order.name}`, icon: \"none\" });\r\n    },\r\n    handleSign(order) {\r\n      this.signOrder = order;\r\n      this.showSignDrawer = true;\r\n      this.signLocation = null;\r\n      this.signPhoto = [];\r\n      this.signRemark = \"\";\r\n      this.chooseLocation();\r\n    },\r\n    chooseLocation() {\r\n      // wx.chooseLocation({\r\n      //   success: (res) => {\r\n      //     this.signLocation = res;\r\n      //   },\r\n      //   fail: (err) => {\r\n      // \tconsole.log(err);\r\n\r\n      //     uni.showToast({ title: '位置选择失败', icon: 'none' });\r\n      //   }\r\n      // });\r\n      this.locationLoading = true;\r\n      wx.getLocation({\r\n        type: \"gcj02\",\r\n\r\n        success: (res) => {\r\n          console.log(res);\r\n\r\n          // 成功获取经纬度后，进行逆地址解析\r\n          this.qqmapsdk.reverseGeocoder({\r\n            location: {\r\n              latitude: res.latitude,\r\n              longitude: res.longitude,\r\n            },\r\n            success: (result) => {\r\n              // 逆解析成功回调\r\n              console.log(\"逆地址解析结果：\", result);\r\n              // 详细的地址信息在 result.result 里\r\n              const addressInfo = result.result.address_component;\r\n              const formattedAddress = result.result.address;\r\n              console.log(\"所在城市：\", addressInfo.city);\r\n              console.log(\"完整地址：\", formattedAddress);\r\n              this.locationLoading = false;\r\n              this.signLocation = formattedAddress;\r\n              // 你可以在这里将地址信息更新到 data 中，或进行其他操作\r\n            },\r\n            fail: function (err) {\r\n              this.locationLoading = false;\r\n              console.error(\"逆地址解析失败：\", err);\r\n              uni.showToast({ title: \"位置解析失败\", icon: \"none\" });\r\n            },\r\n          });\r\n        },\r\n        fail: (err) => {\r\n          console.log(err);\r\n          this.locationLoading = false;\r\n          uni.showToast({ title: \"位置选择失败\", icon: \"none\" });\r\n        },\r\n      });\r\n    },\r\n    afterRead(event) {\r\n      console.log(event);\r\n      const { file } = event;\r\n      const indexAll = this.signPhoto.length;\r\n      file.forEach((item, index) => {\r\n        this.signPhoto.push({\r\n          ...item,\r\n          status: \"uploading\",\r\n          message: \"上传中\",\r\n          // url: item.thumb,\r\n          index: indexAll + index,\r\n        });\r\n      });\r\n      file.forEach((item, index) => {\r\n        this.uploadFile(item.url, indexAll + index);\r\n      });\r\n    },\r\n    uploadFile(url, index) {\r\n      return new Promise((resolve) => {\r\n        const params = {\r\n          filePath: url,\r\n          name: \"file\",\r\n        };\r\n        http.upload(\"/blade-resource/attach/upload\", params).then((res) => {\r\n          this.signPhoto.find((item) => item.index == index).status =\r\n            \"success\";\r\n          this.signPhoto.find((item) => item.index == index).message =\r\n            \"\";\r\n          this.signPhoto.find((item) => item.index == index).url =\r\n            res.data.link;\r\n        });\r\n      });\r\n    },\r\n    handleDelete({ file, index, name }) {\r\n      console.log(file, index, name);\r\n      this.signPhoto.splice(index, 1);\r\n    },\r\n    handleClickPreview(url, lists, name) {\r\n      console.log(url, lists, name);\r\n    },\r\n    submitSign() {\r\n      if (!this.signLocation) {\r\n        uni.showToast({ title: \"请选择位置\", icon: \"none\" });\r\n        return;\r\n      }\r\n      if (!this.signPhoto) {\r\n        uni.showToast({ title: \"请上传照片\", icon: \"none\" });\r\n        return;\r\n      }\r\n      // 这里可以提交签到数据\r\n      uni.showToast({ title: \"签到成功\", icon: \"success\" });\r\n      this.showSignDrawer = false;\r\n    },\r\n    handleFinish(order) {\r\n      uni.showToast({ title: `完成：${order.name}`, icon: \"none\" });\r\n    },\r\n  },\r\n};\r\n</script>\r\n\r\n<style lang=\"scss\">\r\n.workbench-wrap {\r\n  min-height: 100vh;\r\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\r\n  padding: 32rpx 0 0 0;\r\n}\r\n.summary-card {\r\n  margin: 32rpx 32rpx 24rpx 32rpx;\r\n  background: #fff;\r\n  border-radius: 24rpx;\r\n  box-shadow: 0 4rpx 24rpx 0 rgba(0, 0, 0, 0.06);\r\n  padding: 32rpx 24rpx 24rpx 24rpx;\r\n}\r\n.summary-title {\r\n  font-size: 32rpx;\r\n  font-weight: bold;\r\n  margin-bottom: 24rpx;\r\n  color: #333;\r\n}\r\n.summary-stats {\r\n  display: flex;\r\n  justify-content: space-between;\r\n}\r\n.stat-item {\r\n  flex: 1;\r\n  text-align: center;\r\n}\r\n.stat-label {\r\n  font-size: 24rpx;\r\n  color: #888;\r\n  margin-bottom: 8rpx;\r\n}\r\n.stat-value {\r\n  font-size: 36rpx;\r\n  font-weight: 600;\r\n  color: #2d8cf0;\r\n}\r\n.stat-item.total .stat-value {\r\n  color: #2d8cf0;\r\n}\r\n.stat-item.pending .stat-value {\r\n  color: #ff9900;\r\n}\r\n.stat-item.processing .stat-value {\r\n  color: #19be6b;\r\n}\r\n.stat-item.finished .stat-value {\r\n  color: #909399;\r\n}\r\n\r\n.order-tab-bar {\r\n  display: flex;\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  margin: 0 32rpx 16rpx 32rpx;\r\n  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.03);\r\n  overflow: hidden;\r\n}\r\n.tab-item {\r\n  flex: 1;\r\n  text-align: center;\r\n  padding: 24rpx 0;\r\n  font-size: 28rpx;\r\n  color: #888;\r\n  background: #fff;\r\n  transition: all 0.2s;\r\n}\r\n.tab-item.active {\r\n  color: #2d8cf0;\r\n  font-weight: bold;\r\n  background: #e6f7ff;\r\n}\r\n\r\n.order-list {\r\n  margin: 0 32rpx;\r\n}\r\n.order-card {\r\n  background: #fff;\r\n  border-radius: 16rpx;\r\n  box-shadow: 0 2rpx 12rpx 0 rgba(0, 0, 0, 0.04);\r\n  margin-bottom: 24rpx;\r\n  padding: 24rpx 20rpx 16rpx 20rpx;\r\n  transition: box-shadow 0.2s;\r\n}\r\n.order-actions {\r\n  display: flex;\r\n  gap: 24rpx;\r\n  border-top: 1px solid #f0f0f0;\r\n  margin-top: 18rpx;\r\n  padding-top: 18rpx;\r\n  justify-content: flex-end;\r\n  background: #fff;\r\n}\r\n.action-btn {\r\n  min-width: 120rpx;\r\n  padding: 0 32rpx;\r\n  height: 56rpx;\r\n\r\n  line-height: 56rpx;\r\n  border: none;\r\n  border-radius: 32rpx;\r\n  background: #f5f7fa;\r\n  color: #2d8cf0;\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  margin: 0;\r\n  outline: none;\r\n  box-shadow: 0 2rpx 8rpx 0 rgba(45, 140, 240, 0.04);\r\n  transition: background 0.2s, color 0.2s;\r\n}\r\n.action-btn.primary {\r\n  background: linear-gradient(90deg, #2d8cf0 0%, #57a3f3 100%);\r\n  color: #fff;\r\n}\r\n.action-btn:active {\r\n  opacity: 0.85;\r\n}\r\n.order-card:hover {\r\n  box-shadow: 0 8rpx 32rpx 0 rgba(45, 140, 240, 0.08);\r\n}\r\n.order-header {\r\n  display: flex;\r\n  justify-content: space-between;\r\n  align-items: center;\r\n  margin-bottom: 12rpx;\r\n}\r\n.order-title {\r\n  font-size: 28rpx;\r\n  font-weight: 500;\r\n  color: #333;\r\n}\r\n.order-status {\r\n  font-size: 24rpx;\r\n  padding: 4rpx 16rpx;\r\n  border-radius: 16rpx;\r\n  background: #f5f7fa;\r\n}\r\n.order-status.status-pending {\r\n  color: #ff9900;\r\n  background: #fff7e6;\r\n}\r\n.order-status.status-processing {\r\n  color: #19be6b;\r\n  background: #e6ffed;\r\n}\r\n.order-status.status-finished {\r\n  color: #909399;\r\n  background: #f4f4f5;\r\n}\r\n.order-detail-row {\r\n  display: flex;\r\n  align-items: flex-start;\r\n  font-size: 26rpx;\r\n  color: #555;\r\n  margin-bottom: 8rpx;\r\n}\r\n.order-label {\r\n  min-width: 120rpx;\r\n  color: #888;\r\n  font-weight: 400;\r\n}\r\n.order-value {\r\n  flex: 1;\r\n  color: #333;\r\n  word-break: break-all;\r\n}\r\n.empty-tip {\r\n  text-align: center;\r\n  color: #bbb;\r\n  font-size: 28rpx;\r\n  margin: 64rpx 0;\r\n}\r\n</style>\r\n", "import mod from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"; export default mod; export * from \"-!../../../../HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/loader.js??ref--8-oneOf-1-0!../../../../HBuilderX/plugins/uniapp-cli/node_modules/css-loader/dist/cjs.js??ref--8-oneOf-1-1!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/loaders/stylePostLoader.js!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-2!../../../../HBuilderX/plugins/uniapp-cli/node_modules/postcss-loader/src/index.js??ref--8-oneOf-1-3!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/sass-loader/dist/cjs.js??ref--8-oneOf-1-4!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/webpack-preprocess-loader/index.js??ref--8-oneOf-1-5!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/vue-cli-plugin-uni/packages/vue-loader/lib/index.js??vue-loader-options!../../../../HBuilderX/plugins/uniapp-cli/node_modules/@dcloudio/webpack-uni-mp-loader/lib/style.js!./index.vue?vue&type=style&index=0&lang=scss&\"", "// extracted by mini-css-extract-plugin\n    if(module.hot) {\n      // 1758943773545\n      var cssReload = require(\"D:/HBuilderX/plugins/uniapp-cli/node_modules/mini-css-extract-plugin/dist/hmr/hotModuleReplacement.js\")(module.id, {\"hmr\":true,\"publicPath\":\"/\",\"locals\":false});\n      module.hot.dispose(cssReload);\n      module.hot.accept(undefined, cssReload);\n    }\n  "], "sourceRoot": ""}