@charset "UTF-8";
/* 水平间距 */
/* 水平间距 */
.login-container.data-v-b237504c {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #c3cfe2 100%);
  position: relative;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40rpx;
  box-sizing: border-box;
}
.bg-decoration.data-v-b237504c {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  overflow: hidden;
  z-index: 1;
}
.bg-decoration .circle.data-v-b237504c {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  -webkit-animation: float-data-v-b237504c 6s ease-in-out infinite;
          animation: float-data-v-b237504c 6s ease-in-out infinite;
}
.bg-decoration .circle.circle-1.data-v-b237504c {
  width: 200rpx;
  height: 200rpx;
  top: 10%;
  left: -50rpx;
  -webkit-animation-delay: 0s;
          animation-delay: 0s;
}
.bg-decoration .circle.circle-2.data-v-b237504c {
  width: 150rpx;
  height: 150rpx;
  top: 60%;
  right: -30rpx;
  -webkit-animation-delay: 2s;
          animation-delay: 2s;
}
.bg-decoration .circle.circle-3.data-v-b237504c {
  width: 100rpx;
  height: 100rpx;
  top: 30%;
  right: 20%;
  -webkit-animation-delay: 4s;
          animation-delay: 4s;
}
@-webkit-keyframes float-data-v-b237504c {
0%, 100% {
    -webkit-transform: translateY(0px) rotate(0deg);
            transform: translateY(0px) rotate(0deg);
}
50% {
    -webkit-transform: translateY(-20px) rotate(180deg);
            transform: translateY(-20px) rotate(180deg);
}
}
@keyframes float-data-v-b237504c {
0%, 100% {
    -webkit-transform: translateY(0px) rotate(0deg);
            transform: translateY(0px) rotate(0deg);
}
50% {
    -webkit-transform: translateY(-20px) rotate(180deg);
            transform: translateY(-20px) rotate(180deg);
}
}
.login-card.data-v-b237504c {
  background: rgba(255, 255, 255, 0.95);
  -webkit-backdrop-filter: blur(10px);
          backdrop-filter: blur(10px);
  border-radius: 30rpx;
  padding: 60rpx 40rpx;
  width: 100%;
  max-width: 600rpx;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 2;
}
.login-card.data-v-b237504c::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 30rpx;
  padding: 2rpx;
  background: linear-gradient(135deg, #667eea, #c3cfe2);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
          mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: xor;
          mask-composite: exclude;
  z-index: -1;
}
.header-section.data-v-b237504c {
  text-align: center;
  margin-bottom: 60rpx;
}
.header-section .logo-container.data-v-b237504c {
  margin-bottom: 30rpx;
  display: flex;
  justify-content: center;
}
.header-section .logo-container .logo-bg.data-v-b237504c {
  width: 120rpx;
  height: 120rpx;
  background: linear-gradient(135deg, #667eea 0%, #c3cfe2 100%);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 10rpx 30rpx rgba(102, 126, 234, 0.3);
  -webkit-animation: pulse-data-v-b237504c 2s ease-in-out infinite;
          animation: pulse-data-v-b237504c 2s ease-in-out infinite;
}
.header-section .title.data-v-b237504c {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 15rpx;
}
.header-section .subtitle.data-v-b237504c {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}
@-webkit-keyframes pulse-data-v-b237504c {
0%, 100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
}
}
@keyframes pulse-data-v-b237504c {
0%, 100% {
    -webkit-transform: scale(1);
            transform: scale(1);
}
50% {
    -webkit-transform: scale(1.05);
            transform: scale(1.05);
}
}
.form-section .button-section.data-v-b237504c {
  margin: 40rpx 0;
}
.form-section .divider-section.data-v-b237504c {
  display: flex;
  align-items: center;
  margin: 40rpx 0;
}
.form-section .divider-section .divider-line.data-v-b237504c {
  flex: 1;
  height: 1rpx;
  background: #e0e0e0;
}
.form-section .divider-section .divider-text.data-v-b237504c {
  margin: 0 30rpx;
  font-size: 24rpx;
  color: #999;
}
.form-section .wechat-section .quick-phone-section.data-v-b237504c {
  margin-top: 30rpx;
  text-align: center;
}
.form-section .wechat-section .quick-phone-section .quick-phone-text.data-v-b237504c {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 15rpx;
}
.footer-section.data-v-b237504c {
  position: absolute;
  bottom: 60rpx;
  left: 50%;
  -webkit-transform: translateX(-50%);
          transform: translateX(-50%);
  z-index: 2;
}
.footer-section .privacy-text.data-v-b237504c {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.8);
  text-align: center;
  line-height: 1.5;
}
.footer-section .privacy-text .link-text.data-v-b237504c {
  color: #fff;
  text-decoration: underline;
}
@media screen and (max-height: 800px) {
.login-container.data-v-b237504c {
    padding: 20rpx;
}
.login-card.data-v-b237504c {
    padding: 40rpx 30rpx;
}
.header-section.data-v-b237504c {
    margin-bottom: 40rpx;
}
.header-section .logo-container.data-v-b237504c {
    margin-bottom: 20rpx;
}
.footer-section.data-v-b237504c {
    bottom: 30rpx;
}
}
@media (prefers-color-scheme: dark) {
.login-card.data-v-b237504c {
    background: rgba(30, 30, 30, 0.95);
}
.login-card .title.data-v-b237504c {
    color: #fff;
}
.login-card .subtitle.data-v-b237504c {
    color: #ccc;
}
.divider-line.data-v-b237504c {
    background: #444 !important;
}
.divider-text.data-v-b237504c {
    color: #ccc !important;
}
.quick-phone-text.data-v-b237504c {
    color: #ccc !important;
}
}

